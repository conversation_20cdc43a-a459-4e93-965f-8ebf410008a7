package schema

import (
	"reflect"
	"strings"
)

// Field represents a single field in a model's schema.
type Field struct {
	Name        string
	DBName      string // From `json` tag
	ESDataType  string // From `es:"type:..."`
	TagSettings map[string]string
}

// Schema represents the parsed schema of a model.
type Schema struct {
	Model     interface{}
	Name      string
	IndexName string
	Fields    []*Field
}

// Parse takes a model struct and parses its schema.
func Parse(dest interface{}) (*Schema, error) {
	typ := reflect.TypeOf(dest)
	if typ.Kind() == reflect.Ptr {
		typ = typ.Elem()
	}

	schema := &Schema{
		Model: dest,
		Name:  typ.Name(),
		// A simple default for the index name, can be overridden later.
		IndexName: strings.ToLower(typ.Name()) + "s",
	}

	for i := 0; i < typ.NumField(); i++ {
		p := typ.Field(i)
		// We only parse exported fields.
		if p.IsExported() {
			field := &Field{
				Name:        p.Name,
				TagSettings: make(map[string]string),
			}

			// Parse `json` tag
			if jsonTag := p.Tag.Get("json"); jsonTag != "" {
				parts := strings.Split(jsonTag, ",")
				field.DBName = parts[0]
			} else {
				// Default to field name if no json tag
				field.DBName = p.Name
			}

			// Parse `es` tag
			if esTag := p.Tag.Get("es"); esTag != "" {
				parts := strings.Split(esTag, ";")
				for _, part := range parts {
					kv := strings.SplitN(part, ":", 2)
					if len(kv) == 2 {
						key, value := kv[0], kv[1]
						if key == "type" {
							field.ESDataType = value
						} else {
							field.TagSettings[key] = value
						}
					}
				}
			}

			schema.Fields = append(schema.Fields, field)
		}
	}

	return schema, nil
}
