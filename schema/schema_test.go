package schema

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

type User struct {
	ID   string `json:"id" es:"_id"`
	Name string `json:"name" es:"type:text"`
	Age  int    `json:"age" es:"type:integer"`
}

func TestParse(t *testing.T) {
	userSchema, err := Parse(&User{})
	assert.NoError(t, err)

	assert.Equal(t, "User", userSchema.Name)
	assert.Equal(t, "users", userSchema.IndexName)
	assert.Equal(t, 3, len(userSchema.Fields))

	// Test ID field
	assert.Equal(t, "ID", userSchema.Fields[0].Name)
	assert.Equal(t, "id", userSchema.Fields[0].DBName)
	// We don't assign a type to meta fields like _id, so this should be empty
	assert.Equal(t, "", userSchema.Fields[0].ESDataType)

	// Test Name field
	assert.Equal(t, "Name", userSchema.Fields[1].Name)
	assert.Equal(t, "name", userSchema.Fields[1].DBName)
	assert.Equal(t, "text", userSchema.Fields[1].ESDataType)

	// Test Age field
	assert.Equal(t, "Age", userSchema.Fields[2].Name)
	assert.Equal(t, "age", userSchema.Fields[2].DBName)
	assert.Equal(t, "integer", userSchema.Fields[2].ESDataType)
}
