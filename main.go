package main

import (
	"fmt"
	"log"

	"go-es-orm/orm"
)

func main() {
	config := orm.Config{
		Addresses: []string{"http://localhost:9200"},
	}

	db, err := orm.Open(config)
	if err != nil {
		log.Fatalf("Error creating the client: %s", err)
	}

	fmt.Println("Client initialized successfully.")

	type User struct {
		Name string `json:"name"`
	}

	userSession := db.Model(&User{})
	if userSession.Schema == nil {
		log.Fatalf("Schema was not initialized!")
	}
	if userSession.Compiler == nil {
		log.Fatalf("Compiler was not initialized!")
	}

	fmt.Println("Successfully created a model session!")
	fmt.Printf("Model Name: %s, Index Name: %s\n", userSession.Schema.Name, userSession.Schema.IndexName)

	// Test the Where clause
	querySession := userSession.Where("name = ?", "test-user")
	
	// This is a bit of a hack to inspect the internal state for now.
	// We'll have better ways to test this later.
	fmt.Println("Successfully applied a Where clause.")
	// A real test would inspect querySession.clauses, but since it's private,
	// we'll just trust it worked for now and proceed.
	// We'll build the compiler next to make this testable.
}
