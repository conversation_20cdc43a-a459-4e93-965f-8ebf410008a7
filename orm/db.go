package orm

import (
	"go-es-orm/compiler"
	"go-es-orm/schema"

	"github.com/elastic/go-elasticsearch/v8"
)

// Config holds the configuration for the ORM client.
type Config struct {
	// Addresses holds the Elasticsearch node addresses.
	Addresses []string
	// TODO: Add other configuration fields like username, password, API key, etc.
}

// DB is the main struct for interacting with the ORM.
// It holds the underlying Elasticsearch client and session information.
type DB struct {
	ES       *elasticsearch.Client
	Schema   *schema.Schema
	Compiler compiler.Compiler
	clauses  []interface{}
}

// Open initializes a new ORM client with the given configuration.
func Open(config Config) (*DB, error) {
	cfg := elasticsearch.Config{
		Addresses: config.Addresses,
	}

	es, err := elasticsearch.NewClient(cfg)
	if err != nil {
		return nil, err
	}

	return &DB{ES: es}, nil
}

// Model specifies the model instance to be used for the query.
func (db *DB) Model(value interface{}) *DB {
	// This follows the immutable pattern we decided on.
	// It creates a new session for this specific model.
	newSession := &DB{
		ES: db.ES,
	}

	s, err := schema.Parse(value)
	if err != nil {
		// For now, let's panic. We'll implement proper error handling later.
		panic(err)
	}

	newSession.Schema = s
	newSession.Compiler = compiler.New(s)

	return newSession
}

// clone creates a new DB session, preserving the existing session's state.
func (db *DB) clone() *DB {
	newSession := &DB{
		ES:       db.ES,
		Schema:   db.Schema,
		Compiler: db.Compiler,
		clauses:  make([]interface{}, len(db.clauses)),
	}
	copy(newSession.clauses, db.clauses)
	return newSession
}

// Where adds a query condition.
func (db *DB) Where(query interface{}, args ...interface{}) *DB {
	newSession := db.clone()
	// For now, we'll just store the raw query and args.
	// The compiler will handle parsing this later.
	newSession.clauses = append(newSession.clauses, map[string]interface{}{
		"query": query,
		"args":  args,
	})
	return newSession
}
