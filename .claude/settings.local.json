{"permissions": {"allow": ["mcp__ai-develop-assistant__save_clarification_tasks", "mcp__ai-develop-assistant__check_architecture_prerequisites", "mcp__ai-develop-assistant__requirement_clarifier", "mcp__ai-develop-assistant__update_branch_status", "mcp__ai-develop-assistant__get_architecture_design_prompt", "mcp__ai-develop-assistant__save_generated_architecture", "mcp__ai-develop-assistant__export_final_document", "Bash(go mod:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(go run:*)", "mcp__ai-develop-assistant__view_requirements_status", "<PERSON><PERSON>(go test:*)"], "deny": [], "ask": []}}